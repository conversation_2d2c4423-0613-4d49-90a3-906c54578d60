<template>
  <div>
    Personal Center Index
    <GroupCell :cell-list="appointmentCellList" @cell-click="handleCellClick">
      <!-- 自定义第一个Cell的左侧内容 -->
      <template v-slot:left-0="{ cell, index }">
        <div class="custom-left">
          <i
            class="el-icon-tickets"
            style="color: var(--color-primary); margin-right: 8px"
          ></i>
          <span>{{ cell.title }}</span>
        </div>
      </template>
    </GroupCell>

    <GroupCell :cell-list="otherCellList" @cell-click="handleCellClick">
      <template
        v-for="(cellItem, cellIdx) in otherCellList"
        #[`left-${cellIdx}`]="{ cell, index }"
      >
        <div class="custom-left">
          <i
            :class="cellItem.leftIconName"
            style="margin-right: 8px"
            :style="cellItem.leftIconStyle"
          ></i>
          <span>{{ cell.title }}</span>
        </div>
      </template>

      <template
        v-for="(cellItem, cellIdx) in otherCellList"
        #[`right-${cellIdx}`]="{ cell, index }"
      >
        <div class="custom-right">
          <span>{{ cellItem.value }}</span>
        </div>
      </template>
    </GroupCell>
  </div>
</template>

<script setup lang="ts">
import { CellClickPayload, CellData } from "@/components/GroupCell/types";
import GroupCell from "@/components/GroupCell/index.vue";

/** 功能列表实际配置类型 */
export interface FunctionCellData<T = any> extends CellData<T> {
  /** 左侧图标样式 */
  leftIconStyle?: Record<string, any>;
  /** 左侧图标名称 */
  leftIconName: string;
  /** 点击事件自定义处理器 */
  clickHandler?: (cell: FunctionCellData, index: number) => void;
  /** 左侧是slot */
  leftSlot?: boolean;
  /** 右侧是slot */
  rightSlot?: boolean;
}

/** cell额外带的数据 */
interface CellExtData {
  path?: string;
  callBackFn?: (payload: { cell: CellData; index: number }) => void;
}

/** 预约相关列表 */
const appointmentCellList = ref<CellData<CellExtData>[]>([
  {
    title: "我的预约",
    value: "",
    data: {
      path: "/personalCenter/myBooking",
    },
  },
]);

/** 其他功能列表 */
const otherCellList = ref<FunctionCellData<CellExtData>[]>([
  {
    title: "个人信息",
    value: "",
    leftIconName: "el-icon-user",
    rightIcon: "el-icon-arrow-right",
    leftIconStyle: {
      color: "var(--color-primary)",
    },
    data: {
      callBackFn: (payload: CellClickPayload<CellExtData>) => {
        console.log("个人信息被点击:", payload);
      },
    },
  },
  {
    title: "消息中心",
    value: "",
    leftIconName: "el-icon-bell",
    badge: void 0,
    rightIcon: "el-icon-arrow-right",
    leftIconStyle: {
      color: "#e6a23c",
    },
    data: {
      path: "/personalCenter/notifications",
    },
  },
]);

/**
 * Cell点击事件处理
 * @param payload 点击事件数据
 */
const handleCellClick = (payload: CellClickPayload<CellExtData>) => {
  // 可以根据不同的数据类型执行不同的操作
  const { cell } = payload;
  if (cell.data?.path) {
    // 跳转到指定页面
    console.log("跳转到页面:", cell.data.path);
  } else if (cell.data?.callBackFn) {
    // 执行回调函数
    cell.data.callBackFn(payload);
  } else {
    console.log("没有指定操作");
  }
};
</script>

<style scoped lang="scss"></style>
