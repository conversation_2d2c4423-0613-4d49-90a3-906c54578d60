<template>
  <div 
    class="date-card" 
    :class="{ 'active': isActive, 'disabled': isDisabled }"
    @click="handleClick"
  >
    <div class="date-label">{{ dateLabel }}</div>
    <div class="date-number">{{ dateNumber }}</div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";

/**
 * 日期卡片组件Props接口
 */
interface Props {
  /** 日期对象 */
  date: Date;
  /** 是否选中 */
  isActive?: boolean;
  /** 是否禁用 */
  isDisabled?: boolean;
}

/**
 * 组件事件接口
 */
interface Emits {
  (e: "click", date: Date): void;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
  isDisabled: false,
});

const emit = defineEmits<Emits>();

/**
 * 计算日期标签（今天、明天、周几）
 */
const dateLabel = computed(() => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  
  // 重置时间部分进行比较
  const compareDate = new Date(props.date);
  const compareToday = new Date(today);
  const compareTomorrow = new Date(tomorrow);
  
  compareDate.setHours(0, 0, 0, 0);
  compareToday.setHours(0, 0, 0, 0);
  compareTomorrow.setHours(0, 0, 0, 0);
  
  if (compareDate.getTime() === compareToday.getTime()) {
    return "今天";
  } else if (compareDate.getTime() === compareTomorrow.getTime()) {
    return "明天";
  } else {
    const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    return weekDays[props.date.getDay()];
  }
});

/**
 * 计算日期数字（月/日格式）
 */
const dateNumber = computed(() => {
  const month = props.date.getMonth() + 1;
  const day = props.date.getDate();
  return `${month}/${day}`;
});

/**
 * 处理点击事件
 */
const handleClick = () => {
  if (!props.isDisabled) {
    emit("click", props.date);
  }
};
</script>

<style scoped lang="scss">
.date-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-width: 60px;
  height: 70px;
  padding: 8px 12px;
  margin-right: 12px;
  border-radius: 8px;
  background: #ffffff;
  border: 1px solid #e5e5e5;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:last-child {
    margin-right: 0;
  }
  
  .date-label {
    font-size: 12px;
    color: #666666;
    margin-bottom: 4px;
    line-height: 1;
  }
  
  .date-number {
    font-size: 14px;
    font-weight: 500;
    color: #333333;
    line-height: 1;
  }
  
  &.active {
    background: #3a88f5;
    border-color: #3a88f5;
    
    .date-label,
    .date-number {
      color: #ffffff;
    }
  }
  
  &.disabled {
    background: #f5f5f5;
    border-color: #e5e5e5;
    cursor: not-allowed;
    
    .date-label,
    .date-number {
      color: #cccccc;
    }
  }
  
  &:not(.disabled):not(.active):hover {
    background: #f0f8ff;
    border-color: #3a88f5;
  }
}
</style>
