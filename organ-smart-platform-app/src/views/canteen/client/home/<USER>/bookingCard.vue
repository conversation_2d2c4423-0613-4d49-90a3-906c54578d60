<template>
  <div class="booking-card">
    <div class="card-header">
      <h3 class="card-title">预约信息</h3>
    </div>
    
    <div class="booking-info">
      <!-- 餐厅信息 -->
      <div class="info-row">
        <span class="label">餐厅</span>
        <span class="value">{{ canteenName }}</span>
      </div>
      
      <!-- 日期信息 -->
      <div class="info-row">
        <span class="label">日期</span>
        <span class="value">{{ formattedDate }}</span>
      </div>
      
      <!-- 餐次信息 -->
      <div class="info-row">
        <span class="label">餐次({{ selectedMeals.length }}个)</span>
        <span class="value">{{ mealTimesText }}</span>
      </div>
      
      <!-- 预约数量 -->
      <div class="info-row">
        <span class="label">预约数量</span>
        <span class="value highlight">{{ selectedMeals.length }}个餐次</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { IMealPeriod } from "@/api/canteen/client/types/mealPeriod";

/**
 * 预约信息卡片组件Props接口
 */
interface Props {
  /** 食堂名称 */
  canteenName: string;
  /** 选择的日期 */
  selectedDate: Date;
  /** 选择的餐次列表 */
  selectedMeals: IMealPeriod[];
}

const props = defineProps<Props>();

/**
 * 格式化日期显示
 */
const formattedDate = computed(() => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  
  // 重置时间部分进行比较
  const compareDate = new Date(props.selectedDate);
  const compareToday = new Date(today);
  const compareTomorrow = new Date(tomorrow);
  
  compareDate.setHours(0, 0, 0, 0);
  compareToday.setHours(0, 0, 0, 0);
  compareTomorrow.setHours(0, 0, 0, 0);
  
  const month = props.selectedDate.getMonth() + 1;
  const day = props.selectedDate.getDate();
  const dateStr = `${month}/${day}`;
  
  if (compareDate.getTime() === compareToday.getTime()) {
    return `今天 ${dateStr}`;
  } else if (compareDate.getTime() === compareTomorrow.getTime()) {
    return `明天 ${dateStr}`;
  } else {
    const weekDays = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"];
    const weekDay = weekDays[props.selectedDate.getDay()];
    return `${weekDay} ${dateStr}`;
  }
});

/**
 * 格式化餐次时间文本
 */
const mealTimesText = computed(() => {
  if (props.selectedMeals.length === 0) {
    return "未选择";
  }
  
  return props.selectedMeals
    .map(meal => `${meal.startTime} - ${meal.endTime}`)
    .join("，");
});
</script>

<style scoped lang="scss">
.booking-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid #e5e5e5;
  
  .card-header {
    margin-bottom: 16px;
    
    .card-title {
      font-size: 16px;
      font-weight: 600;
      color: #333333;
      margin: 0;
    }
  }
  
  .booking-info {
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #f5f5f5;
      
      &:last-child {
        border-bottom: none;
      }
      
      .label {
        font-size: 14px;
        color: #666666;
        flex-shrink: 0;
      }
      
      .value {
        font-size: 14px;
        color: #333333;
        text-align: right;
        flex: 1;
        margin-left: 16px;
        
        &.highlight {
          color: #3a88f5;
          font-weight: 500;
        }
      }
    }
  }
}
</style>
