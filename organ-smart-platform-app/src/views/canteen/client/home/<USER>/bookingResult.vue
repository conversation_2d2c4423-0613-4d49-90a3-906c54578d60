<template>
  <div class="booking-result-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="booking-result-dialog" @click.stop>
      <!-- 成功状态 -->
      <div v-if="result.success && result.failedCount === 0" class="result-content success">
        <div class="result-icon">
          <i class="el-icon-success"></i>
        </div>
        <h3 class="result-title">预约成功</h3>
        <p class="result-message">{{ result.message }}</p>
        
        <div class="success-meals">
          <div v-for="meal in result.successMeals" :key="meal.mealPeriodId" class="meal-item">
            <span class="meal-name">{{ meal.mealName }}</span>
            <span class="meal-time">{{ meal.bookingTime }}</span>
          </div>
        </div>
        
        <div class="result-actions">
          <button class="btn-primary" @click="handleConfirm">确定</button>
        </div>
      </div>
      
      <!-- 部分成功状态 -->
      <div v-else-if="result.success && result.failedCount > 0" class="result-content partial">
        <div class="result-icon">
          <i class="el-icon-warning"></i>
        </div>
        <h3 class="result-title">部分预约成功</h3>
        <p class="result-message">{{ `成功预约${result.successCount}个餐次，${result.failedCount}个餐次预约失败` }}</p>
        
        <!-- 成功的餐次 -->
        <div v-if="result.successMeals.length > 0" class="success-section">
          <h4 class="section-title success">预约成功</h4>
          <div class="success-meals">
            <div v-for="meal in result.successMeals" :key="meal.mealPeriodId" class="meal-item">
              <span class="meal-name">{{ meal.mealName }}</span>
              <span class="meal-time">{{ meal.bookingTime }}</span>
            </div>
          </div>
        </div>
        
        <!-- 失败的餐次 -->
        <div v-if="result.failedMeals.length > 0" class="failed-section">
          <h4 class="section-title failed">预约失败</h4>
          <div class="failed-meals">
            <div v-for="meal in result.failedMeals" :key="meal.mealPeriodId" class="meal-item">
              <span class="meal-name">{{ meal.mealName }}</span>
              <span class="meal-reason">{{ meal.reason }}</span>
            </div>
          </div>
        </div>
        
        <div class="result-actions">
          <button class="btn-secondary" @click="handleContinueBooking">继续预约</button>
          <button class="btn-primary" @click="handleConfirm">确定</button>
        </div>
      </div>
      
      <!-- 完全失败状态 -->
      <div v-else class="result-content failed">
        <div class="result-icon">
          <i class="el-icon-error"></i>
        </div>
        <h3 class="result-title">预约失败</h3>
        <p class="result-message">{{ result.message }}</p>
        
        <div v-if="result.failedMeals.length > 0" class="failed-section">
          <div class="failed-meals">
            <div v-for="meal in result.failedMeals" :key="meal.mealPeriodId" class="meal-item">
              <span class="meal-name">{{ meal.mealName }}</span>
              <span class="meal-reason">{{ meal.reason }}</span>
            </div>
          </div>
        </div>
        
        <div class="result-actions">
          <button class="btn-secondary" @click="handleContinueBooking">继续预约</button>
          <button class="btn-primary" @click="handleConfirm">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { IMealBookingResponse } from "@/api/canteen/client/types/mealPeriod";

/**
 * 预约结果弹窗组件Props接口
 */
interface Props {
  /** 是否显示弹窗 */
  visible: boolean;
  /** 预约结果数据 */
  result: IMealBookingResponse;
}

/**
 * 组件事件接口
 */
interface Emits {
  (e: "update:visible", visible: boolean): void;
  (e: "confirm"): void;
  (e: "continue-booking"): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 处理遮罩层点击事件
 */
const handleOverlayClick = () => {
  emit("update:visible", false);
};

/**
 * 处理确定按钮点击事件
 */
const handleConfirm = () => {
  emit("confirm");
  emit("update:visible", false);
};

/**
 * 处理继续预约按钮点击事件
 */
const handleContinueBooking = () => {
  emit("continue-booking");
  emit("update:visible", false);
};
</script>

<style scoped lang="scss">
.booking-result-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  
  .booking-result-dialog {
    background: #ffffff;
    border-radius: 12px;
    max-width: 400px;
    width: 100%;
    max-height: 80vh;
    overflow-y: auto;
    
    .result-content {
      padding: 24px;
      text-align: center;
      
      .result-icon {
        margin-bottom: 16px;
        
        i {
          font-size: 48px;
        }
      }
      
      .result-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 8px 0;
      }
      
      .result-message {
        font-size: 14px;
        color: #666666;
        margin: 0 0 20px 0;
        line-height: 1.4;
      }
      
      &.success {
        .result-icon i {
          color: #52c41a;
        }
        
        .result-title {
          color: #52c41a;
        }
      }
      
      &.partial {
        .result-icon i {
          color: #fa8c16;
        }
        
        .result-title {
          color: #fa8c16;
        }
      }
      
      &.failed {
        .result-icon i {
          color: #ff4d4f;
        }
        
        .result-title {
          color: #ff4d4f;
        }
      }
    }
    
    .success-section,
    .failed-section {
      margin-bottom: 20px;
      text-align: left;
      
      .section-title {
        font-size: 14px;
        font-weight: 500;
        margin: 0 0 8px 0;
        
        &.success {
          color: #52c41a;
        }
        
        &.failed {
          color: #ff4d4f;
        }
      }
    }
    
    .success-meals,
    .failed-meals {
      .meal-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .meal-name {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
        }
        
        .meal-time {
          font-size: 12px;
          color: #666666;
        }
        
        .meal-reason {
          font-size: 12px;
          color: #ff4d4f;
        }
      }
    }
    
    .result-actions {
      display: flex;
      gap: 12px;
      margin-top: 24px;
      
      button {
        flex: 1;
        padding: 12px 20px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        
        &.btn-primary {
          background: #3a88f5;
          color: #ffffff;
          
          &:hover {
            background: #2c7ae0;
          }
        }
        
        &.btn-secondary {
          background: #f5f5f5;
          color: #666666;
          
          &:hover {
            background: #e8e8e8;
          }
        }
      }
    }
  }
}
</style>
