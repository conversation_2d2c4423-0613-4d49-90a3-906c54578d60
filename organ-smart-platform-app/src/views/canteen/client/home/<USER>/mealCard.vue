<template>
  <div
    class="meal-card"
    :class="{
      selected: isSelected,
      disabled: isDisabled,
      full: isFull,
    }"
    @click="handleClick"
  >
    <!-- 餐次图标和名称 -->
    <div class="meal-header">
      <div class="meal-icon">
        <i :class="mealIconClass"></i>
      </div>
      <div class="meal-info">
        <div class="meal-name">{{ mealData.mealName }}</div>
        <div class="meal-time">
          {{ `${mealData.startTime}-${mealData.endTime}` }}
        </div>
      </div>
    </div>

    <!-- 状态按钮区域 -->
    <div class="meal-action">
      <!-- 可预约状态 -->
      <div
        v-if="mealData.bookingAvailableStatus === 'OPEN'"
        class="action-btn available"
        :class="{ selected: isSelected }"
      >
        {{ isSelected ? "已选" : "可预约" }}
        <i v-if="isSelected" class="el-icon-check"></i>
      </div>

      <!-- 已约满状态 -->
      <div
        v-else-if="mealData.bookingAvailableStatus === 'FULL'"
        class="full-section"
      >
        <div class="action-btn full">已约满</div>
        <div class="urge-section">
          <button
            v-if="!mealData.isUrged"
            class="urge-btn"
            @click.stop="handleUrgeClick"
          >
            一键催发
          </button>
          <button v-else class="urge-btn urged" disabled>已催发</button>
        </div>
        <!-- 催发提示信息 -->
        <div v-if="mealData.isUrged" class="urge-tip">
          已为您提交催发请求，请耐心等待
        </div>
      </div>

      <!-- 关闭状态 -->
      <div v-else class="action-btn closed">已关闭</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { IMealPeriod } from "@/api/canteen/client/types/mealPeriod";

/**
 * 餐次卡片组件Props接口
 */
interface Props {
  /** 餐次数据 */
  mealData: IMealPeriod;
  /** 是否选中 */
  isSelected?: boolean;
}

/**
 * 组件事件接口
 */
interface Emits {
  (e: "click", mealData: IMealPeriod): void;
  (e: "urge", mealData: IMealPeriod): void;
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
});

const emit = defineEmits<Emits>();

/**
 * 计算是否禁用
 */
const isDisabled = computed(() => {
  return (
    props.mealData.status === "DISABLED" ||
    props.mealData.status === "CLOSED" ||
    props.mealData.bookingAvailableStatus === "CLOSED"
  );
});

/**
 * 计算是否已约满
 */
const isFull = computed(() => {
  return props.mealData.bookingAvailableStatus === "FULL";
});

/**
 * 计算餐次图标类名
 */
const mealIconClass = computed(() => {
  const mealName = props.mealData.mealName.toLowerCase();
  if (mealName.includes("早") || mealName.includes("breakfast")) {
    return "el-icon-sunrise-1";
  } else if (mealName.includes("午") || mealName.includes("lunch")) {
    return "el-icon-sunny";
  } else if (mealName.includes("晚") || mealName.includes("dinner")) {
    return "el-icon-moon";
  }
  return "el-icon-food";
});

/**
 * 处理卡片点击事件
 */
const handleClick = () => {
  // 只有可预约状态才能点击选择
  if (props.mealData.bookingAvailableStatus === "OPEN" && !isDisabled.value) {
    emit("click", props.mealData);
  }
};

/**
 * 处理催发点击事件
 */
const handleUrgeClick = () => {
  emit("urge", props.mealData);
};
</script>

<style scoped lang="scss">
.meal-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  margin-bottom: 12px;
  background: #ffffff;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  .meal-header {
    display: flex;
    align-items: center;
    flex: 1;

    .meal-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #f0f8ff;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 12px;

      i {
        font-size: 20px;
        color: #3a88f5;
      }
    }

    .meal-info {
      .meal-name {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 4px;
      }

      .meal-time {
        font-size: 14px;
        color: #666666;
      }
    }
  }

  .meal-action {
    display: flex;
    flex-direction: column;
    align-items: flex-end;

    .action-btn {
      padding: 6px 16px;
      border-radius: 16px;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 4px;

      &.available {
        background: #e8f4fd;
        color: #3a88f5;
        border: 1px solid #3a88f5;

        &.selected {
          background: #3a88f5;
          color: #ffffff;
        }
      }

      &.full {
        background: #fef0f0;
        color: #f56c6c;
        border: 1px solid #f56c6c;
      }

      &.closed {
        background: #f5f5f5;
        color: #999999;
        border: 1px solid #e5e5e5;
      }
    }

    .full-section {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 8px;

      .urge-section {
        .urge-btn {
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          border: none;
          cursor: pointer;
          transition: all 0.3s ease;

          &:not(.urged) {
            background: #3a88f5;
            color: #ffffff;

            &:hover {
              background: #2c7ae0;
            }
          }

          &.urged {
            background: #f5f5f5;
            color: #999999;
            cursor: not-allowed;
          }
        }
      }

      .urge-tip {
        font-size: 12px;
        color: #999999;
        text-align: right;
        line-height: 1.2;
        max-width: 120px;
      }
    }
  }

  &.selected {
    border-color: #3a88f5;
    background: #f8fbff;

    .meal-header .meal-icon {
      background: #3a88f5;

      i {
        color: #ffffff;
      }
    }
  }

  &.disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:not(.disabled):not(.full):hover {
    border-color: #3a88f5;
    cursor: pointer;
  }
}
</style>
