# 预约中心功能说明

## 根据 UI 实现页面开发

1. 界面 UI 设计稿参考连接未：<https://js.design/f/XygDNv?mode=design&p=ArjQu3_YZx>
2. 请一比一实现 UI 设计稿中的功能，没有的图片或者图标请使用一个对应的元素占位先

## 页面组件说明

1. 顶部区域写在 booking.vue 页面中
2. 日期选择卡片项作为单独的组件编写，放在 booking.vue 文件所在文件夹的\_comp 文件夹中，文件名为 dateCard.vue
3. 预约信息卡片项作为单独的组件编写，放在 booking.vue 文件所在文件夹的\_comp 文件夹中，文件名为 bookingCard.vue
4. 餐次信息卡片项作为单独的组件编写，放在 booking.vue 文件所在文件夹的\_comp 文件夹中，文件名为 mealCard.vue
5. 预约结果弹窗作为单独的组件编写，放在 booking.vue 文件所在文件夹的\_comp 文件夹中，文件名为 bookingResult.vue

## 业务逻辑说明

1. 进入页面后根据路由传递过来的食堂 id 获取食堂信息
   1. 在对应的 api 中实现获取食堂信息的方法
   2. 在页面中调用 api 获取食堂信息并展示
2. 进入页面后获取提前预约天数配置
   1. 创建一个新的 API 文件和类型文件，API 文件取名为 systemConfig.ts，具体内部实现参考 canteenManage.ts
   2. 在对应的 api 中实现获取提前预约天数配置的方法
   3. 根据可预约天数配置计算出可预约的日期范围，并展示对应的日期卡片列表，可左右滑动
   4. 切换日期小卡片时，预约信息小卡片显示当前日期已选择的餐次信息
3. 进入页面后根据食堂 id 获取食堂的可预约餐次信息
   1. 创建一个新的 API 文件和类型文件，API 文件取名为 mealPeriod.ts，具体内部实现参考 canteenManage.ts
   2. 在对应的 api 中实现获取可预约餐次信息的方法
   3. 在页面中调用 api 获取可预约餐次信息并展示
   4. 可预约餐次信息中包含可预约的席位数，如果可预约的席位数为 0，则表示已约满，需要展示已约满的状态
   5. 如果可预约的餐次信息为空，则表示没有可预约的餐次，需要展示没有可预约的餐次的状态
   6. 如果餐次可预约，则点击餐次小卡片的切换所点击餐次的选中状态
   7. 如果餐次已约满，则点击餐次小卡片无任何响应
      1. 餐次已约满时，展示已约满的状态并且展示“一键催发”按钮，点击后触发催发请求
      2. 餐次已约满时，如果已经催发，则按钮展示为“已催发”状态，不可点击，并在餐次卡片底部显示已催发的提示信息
      3. 催发请求成功后，重新获取可预约餐次信息并展示
4. 预约操作
   1. 点击确认预约按钮后提交预约请求
   2. 预约成功后跳转到预约成功页面
   3. 预约失败后展示失败提示
   4. 失败和成功的提示均在当前页面弹框展示
   5. 提交后刷新可预约餐次信息
   6. 提交后清除已勾选的餐次（失败的餐次继续保留勾选状态）
   7. 预约可能存在部分成功+部分失败
   8. 失败信息卡片中展示继续预约（点击继续预约则继续留在当前页面并激活对应的日期卡片去继续预约）
