<template>
  <div class="booking-page">
    <MobileHeader title="预约中心" />

    <div class="page-content">
      <!-- 食堂信息卡片 -->
      <div class="canteen-info-card">
        <div class="canteen-image">
          <!-- 使用占位图片 -->
          <div class="image-placeholder">
            <i class="el-icon-picture-outline"></i>
          </div>
        </div>
        <div class="canteen-details">
          <h2 class="canteen-name">
            {{ canteenInfo?.canteenName || "加载中..." }}
          </h2>
          <p class="canteen-address">
            <i class="el-icon-location-outline"></i>
            {{ canteenInfo?.canteenAddress || "加载中..." }}
          </p>
        </div>
      </div>

      <!-- 选择日期 -->
      <div class="section">
        <h3 class="section-title">选择日期</h3>
        <div class="date-selector">
          <div class="date-list" ref="dateListRef">
            <DateCard
              v-for="date in availableDates"
              :key="date.getTime()"
              :date="date"
              :is-active="isDateSelected(date)"
              :is-disabled="isDateDisabled(date)"
              @click="handleDateSelect"
            />
          </div>
        </div>
      </div>

      <!-- 选择餐次 -->
      <div class="section">
        <h3 class="section-title">选择餐次</h3>
        <div class="meal-list">
          <div v-if="mealPeriodsLoading" class="loading-state">
            <i class="el-icon-loading"></i>
            <span>加载餐次信息中...</span>
          </div>
          <div v-else-if="mealPeriods.length === 0" class="empty-state">
            <i class="el-icon-warning-outline"></i>
            <span>暂无可预约的餐次</span>
          </div>
          <div v-else class="meal-cards">
            <MealCard
              v-for="meal in mealPeriods"
              :key="meal.id"
              :meal-data="meal"
              :is-selected="isMealSelected(meal)"
              @click="handleMealSelect"
              @urge="handleMealUrge"
            />
          </div>
        </div>
      </div>

      <!-- 预约信息 -->
      <div v-if="selectedMeals.length > 0" class="section">
        <BookingCard
          :canteen-name="canteenInfo?.canteenName || ''"
          :selected-date="selectedDate"
          :selected-meals="selectedMeals"
        />
      </div>

      <!-- 预约须知 -->
      <div class="section">
        <div class="booking-notice">
          <div class="notice-header">
            <i class="el-icon-info"></i>
            <span>预约须知</span>
          </div>
          <ul class="notice-list">
            <li>请在预约时间内到达餐厅就餐</li>
            <li>如需取消预约，请提前2小时操作</li>
            <li>逾期未到将被记录，影响后续预约</li>
            <li>恶意预约的将被限制使用</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 底部确认按钮 -->
    <div class="bottom-actions">
      <button
        class="confirm-btn"
        :class="{ disabled: selectedMeals.length === 0 || submitting }"
        :disabled="selectedMeals.length === 0 || submitting"
        @click="handleConfirmBooking"
      >
        <i v-if="submitting" class="el-icon-loading"></i>
        {{
          submitting ? "预约中..." : `确认预约（${selectedMeals.length}个餐次）`
        }}
      </button>
    </div>

    <!-- 预约结果弹窗 -->
    <BookingResult
      v-model:visible="showBookingResult"
      :result="bookingResult"
      @confirm="handleBookingResultConfirm"
      @continue-booking="handleContinueBooking"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from "vue";
import { useRoute } from "vue-router/composables";
import MobileHeader from "@/components/MobileHeader/index.vue";
import DateCard from "./_comp/dateCard.vue";
import MealCard from "./_comp/mealCard.vue";
import BookingCard from "./_comp/bookingCard.vue";
import BookingResult from "./_comp/bookingResult.vue";

// API imports
import { getCanteenById } from "@/api/canteen/client/canteenManage";
import { getAdvanceBookingDays } from "@/api/canteen/client/systemConfig";
import {
  getMealPeriodsByCanteenAndDate,
  submitBooking,
  submitUrgeRequest,
} from "@/api/canteen/client/mealPeriod";

// Types
import { ICanteenInfo } from "@/api/canteen/client/types/canteenManage";
import {
  IMealPeriod,
  IMealBookingRequest,
  IMealBookingResponse,
} from "@/api/canteen/client/types/mealPeriod";

const route = useRoute();

// 响应式数据
const canteenInfo = ref<ICanteenInfo | null>(null);
const availableDates = ref<Date[]>([]);
const selectedDate = ref<Date>(new Date());
const mealPeriods = ref<IMealPeriod[]>([]);
const selectedMeals = ref<IMealPeriod[]>([]);
const mealPeriodsLoading = ref(false);
const submitting = ref(false);
const showBookingResult = ref(false);
const bookingResult = ref<IMealBookingResponse>({
  success: false,
  message: "",
  successMeals: [],
  failedMeals: [],
  totalCount: 0,
  successCount: 0,
  failedCount: 0,
});

// DOM引用
const dateListRef = ref<HTMLElement>();

/**
 * 获取食堂ID（从路由参数中获取）
 */
const canteenId = computed(() => {
  return route.query.canteenId as string;
});

/**
 * 判断日期是否被选中
 */
const isDateSelected = (date: Date): boolean => {
  const compareDate = new Date(date);
  const compareSelected = new Date(selectedDate.value);

  compareDate.setHours(0, 0, 0, 0);
  compareSelected.setHours(0, 0, 0, 0);

  return compareDate.getTime() === compareSelected.getTime();
};

/**
 * 判断日期是否禁用
 */
const isDateDisabled = (date: Date): boolean => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const compareDate = new Date(date);
  compareDate.setHours(0, 0, 0, 0);

  return compareDate.getTime() < today.getTime();
};

/**
 * 判断餐次是否被选中
 */
const isMealSelected = (meal: IMealPeriod): boolean => {
  return selectedMeals.value.some((selected) => selected.id === meal.id);
};

/**
 * 处理日期选择
 */
const handleDateSelect = async (date: Date) => {
  if (isDateDisabled(date)) return;

  selectedDate.value = date;
  selectedMeals.value = []; // 清空已选餐次
  await fetchMealPeriods();
};

/**
 * 处理餐次选择
 */
const handleMealSelect = (meal: IMealPeriod) => {
  const index = selectedMeals.value.findIndex(
    (selected) => selected.id === meal.id
  );

  if (index > -1) {
    // 取消选择
    selectedMeals.value.splice(index, 1);
  } else {
    // 添加选择
    selectedMeals.value.push(meal);
  }
};

/**
 * 处理餐次催发
 */
const handleMealUrge = async (meal: IMealPeriod) => {
  try {
    const urgeRequest = {
      canteenId: canteenId.value,
      mealPeriodId: meal.id,
      bookingDate: formatDateToString(selectedDate.value),
      reason: "用户主动催发",
    };

    const result = await submitUrgeRequest(urgeRequest);

    if (result.success) {
      // 更新餐次状态
      const mealIndex = mealPeriods.value.findIndex((m) => m.id === meal.id);
      if (mealIndex > -1) {
        mealPeriods.value[mealIndex].isUrged = true;
        mealPeriods.value[mealIndex].urgeCount += 1;
      }

      // 显示成功提示
      console.log("催发成功:", result.message);
    }
  } catch (error) {
    console.error("催发失败:", error);
  }
};

/**
 * 验证预约请求
 */
const validateBookingRequest = (): { valid: boolean; message: string } => {
  if (!canteenId.value) {
    return { valid: false, message: "缺少食堂信息" };
  }

  if (selectedMeals.value.length === 0) {
    return { valid: false, message: "请选择至少一个餐次" };
  }

  // 检查是否有已约满的餐次被选中
  const fullMeals = selectedMeals.value.filter(
    (meal) => meal.bookingAvailableStatus === "FULL"
  );
  if (fullMeals.length > 0) {
    return {
      valid: false,
      message: `${fullMeals
        .map((m) => m.mealName)
        .join("、")}已约满，请重新选择`,
    };
  }

  // 检查是否有关闭的餐次被选中
  const closedMeals = selectedMeals.value.filter(
    (meal) =>
      meal.bookingAvailableStatus === "CLOSED" || meal.status === "CLOSED"
  );
  if (closedMeals.length > 0) {
    return {
      valid: false,
      message: `${closedMeals
        .map((m) => m.mealName)
        .join("、")}已关闭，请重新选择`,
    };
  }

  return { valid: true, message: "" };
};

/**
 * 处理确认预约
 */
const handleConfirmBooking = async () => {
  if (submitting.value) return;

  // 验证预约请求
  const validation = validateBookingRequest();
  if (!validation.valid) {
    console.error("预约验证失败:", validation.message);
    // 这里可以显示错误提示
    return;
  }

  try {
    submitting.value = true;

    const bookingRequest: IMealBookingRequest = {
      canteenId: canteenId.value,
      bookingDate: formatDateToString(selectedDate.value),
      mealPeriodIds: selectedMeals.value.map((meal) => meal.id),
      peopleCount: 1,
      remark: "",
    };

    const result = await submitBooking(bookingRequest);
    bookingResult.value = result;
    showBookingResult.value = true;

    // 重新获取餐次信息
    await fetchMealPeriods();

    // 清空成功预约的餐次选择
    if (result.successMeals.length > 0) {
      const successIds = result.successMeals.map((meal) => meal.mealPeriodId);
      selectedMeals.value = selectedMeals.value.filter(
        (meal) => !successIds.includes(meal.id)
      );
    }
  } catch (error) {
    console.error("预约失败:", error);

    // 显示错误结果
    bookingResult.value = {
      success: false,
      message: "网络错误，预约失败，请稍后重试",
      successMeals: [],
      failedMeals: selectedMeals.value.map((meal) => ({
        mealPeriodId: meal.id,
        mealName: meal.mealName,
        reason: "网络错误",
        bookingDate: formatDateToString(selectedDate.value),
      })),
      totalCount: selectedMeals.value.length,
      successCount: 0,
      failedCount: selectedMeals.value.length,
    };
    showBookingResult.value = true;
  } finally {
    submitting.value = false;
  }
};

/**
 * 处理预约结果确认
 */
const handleBookingResultConfirm = () => {
  // 可以在这里添加跳转到预约记录页面的逻辑
  console.log("预约结果已确认");
};

/**
 * 处理继续预约
 */
const handleContinueBooking = () => {
  // 保持在当前页面，用户可以继续选择餐次
  console.log("继续预约");
};

/**
 * 格式化日期为字符串 (YYYY-MM-DD)
 */
const formatDateToString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

/**
 * 获取食堂信息
 */
const fetchCanteenInfo = async () => {
  try {
    if (!canteenId.value) {
      console.error("缺少食堂ID参数");
      return;
    }

    const info = await getCanteenById(canteenId.value);
    canteenInfo.value = info;
  } catch (error) {
    console.error("获取食堂信息失败:", error);
  }
};

/**
 * 获取可预约日期列表
 */
const fetchAvailableDates = async () => {
  try {
    const advanceDays = await getAdvanceBookingDays();
    const dates: Date[] = [];
    const today = new Date();

    for (let i = 0; i < advanceDays; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    availableDates.value = dates;

    // 设置默认选中今天
    if (dates.length > 0) {
      selectedDate.value = dates[0];
    }
  } catch (error) {
    console.error("获取可预约日期失败:", error);
  }
};

/**
 * 获取餐次信息
 */
const fetchMealPeriods = async () => {
  try {
    if (!canteenId.value) return;

    mealPeriodsLoading.value = true;
    const dateString = formatDateToString(selectedDate.value);
    const periods = await getMealPeriodsByCanteenAndDate(
      canteenId.value,
      dateString
    );
    mealPeriods.value = periods;
  } catch (error) {
    console.error("获取餐次信息失败:", error);
    mealPeriods.value = [];
  } finally {
    mealPeriodsLoading.value = false;
  }
};

/**
 * 初始化页面数据
 */
const initPageData = async () => {
  await Promise.all([fetchCanteenInfo(), fetchAvailableDates()]);

  // 获取默认日期的餐次信息
  if (selectedDate.value) {
    await fetchMealPeriods();
  }
};

// 页面挂载时初始化数据
onMounted(() => {
  initPageData();
});
</script>

<style scoped lang="scss">
.booking-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 80px; // 为底部按钮留出空间

  .page-content {
    padding: 16px;

    .canteen-info-card {
      background: #ffffff;
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .canteen-image {
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 16px;
        flex-shrink: 0;

        .image-placeholder {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;

          i {
            font-size: 32px;
            color: #ffffff;
          }
        }
      }

      .canteen-details {
        flex: 1;

        .canteen-name {
          font-size: 18px;
          font-weight: 600;
          color: #333333;
          margin: 0 0 8px 0;
        }

        .canteen-address {
          font-size: 14px;
          color: #666666;
          margin: 0;
          display: flex;
          align-items: center;

          i {
            margin-right: 4px;
            color: #999999;
          }
        }
      }
    }

    .section {
      margin-bottom: 20px;

      .section-title {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
        margin: 0 0 12px 0;
      }
    }

    .date-selector {
      .date-list {
        display: flex;
        overflow-x: auto;
        padding: 8px 0;

        &::-webkit-scrollbar {
          display: none;
        }
      }
    }

    .meal-list {
      .loading-state,
      .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        color: #999999;

        i {
          font-size: 32px;
          margin-bottom: 8px;
        }

        span {
          font-size: 14px;
        }
      }

      .loading-state i {
        animation: rotate 1s linear infinite;
      }

      .meal-cards {
        display: flex;
        flex-direction: column;
        gap: 12px;
      }
    }

    .booking-notice {
      background: #ffffff;
      border-radius: 8px;
      padding: 16px;
      border-left: 4px solid #3a88f5;

      .notice-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        i {
          font-size: 16px;
          color: #3a88f5;
          margin-right: 8px;
        }

        span {
          font-size: 14px;
          font-weight: 500;
          color: #333333;
        }
      }

      .notice-list {
        margin: 0;
        padding-left: 16px;

        li {
          font-size: 12px;
          color: #666666;
          line-height: 1.5;
          margin-bottom: 4px;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .bottom-actions {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: #ffffff;
    padding: 16px;
    border-top: 1px solid #e5e5e5;
    z-index: 100;

    .confirm-btn {
      width: 100%;
      height: 48px;
      background: #3a88f5;
      color: #ffffff;
      border: none;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;

      &:hover:not(.disabled) {
        background: #2c7ae0;
      }

      &.disabled {
        background: #cccccc;
        cursor: not-allowed;
      }

      i {
        animation: rotate 1s linear infinite;
      }
    }
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
