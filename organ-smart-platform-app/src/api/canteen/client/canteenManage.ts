import service from "@/service";
import { ICanteenInfo } from "./types/canteenManage";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";
import { AxiosResponse } from "axios";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/canteenInfo`;

/** 是否模拟数据 */
const isMock = true;

/** 获取所有食堂列表 */
export function getCanteenList(params: Record<string, any> = {}) {
  if (isMock) {
    // 生成5个食堂的基本信息
    return new Promise<ICanteenInfo[]>((resolve) => {
      const list = Array.from({ length: 5 }, (_, index) => ({
        id: `${index + 1}`,
        canteenName: `食堂${index + 1}`,
        canteenAddress: `地址${index + 1}`,
      })) as ICanteenInfo[];
      resolve(list);
    });
  }
  return service.get<any, ICanteenInfo[]>(`${MODULE_API_PREFIX}/list`, params);
}
