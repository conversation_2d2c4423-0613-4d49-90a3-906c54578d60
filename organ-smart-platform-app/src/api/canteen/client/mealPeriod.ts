import service from "@/service";
import {
  IMealPeriod,
  IMealBookingRequest,
  IMealBookingResponse,
  IUrgeRequest,
} from "./types/mealPeriod";
import { BIZ_MODULE_API_PREFIX } from "@/api/canteen/constant";

/** 模块API前缀 */
const MODULE_API_PREFIX = `${BIZ_MODULE_API_PREFIX}/mealPeriod`;

/** 是否模拟数据 */
const isMock = true;

/**
 * 根据食堂ID和日期获取可预约餐次信息
 * @param canteenId 食堂ID
 * @param date 预约日期 (YYYY-MM-DD格式)
 * @returns 餐次信息列表
 */
export function getMealPeriodsByCanteenAndDate(
  canteenId: string,
  date: string
) {
  if (isMock) {
    return new Promise<IMealPeriod[]>((resolve) => {
      const mealPeriods: IMealPeriod[] = [
        {
          id: "1",
          canteenId,
          mealName: "午餐",
          startTime: "11:00",
          endTime: "13:00",
          advanceBookingSeats: 100,
          availableSeats: 85,
          tempBookingSeats: 20,
          status: "AVAILABLE",
          bookingAvailableStatus: "OPEN",
          isUrged: false,
          urgeCount: 0,
        },
        {
          id: "2",
          canteenId,
          mealName: "晚餐",
          startTime: "17:00",
          endTime: "19:00",
          advanceBookingSeats: 80,
          availableSeats: 0,
          tempBookingSeats: 15,
          status: "AVAILABLE",
          bookingAvailableStatus: "FULL",
          isUrged: false,
          urgeCount: 0,
        },
      ];
      resolve(mealPeriods);
    });
  }
  return service.get<any, IMealPeriod[]>(`${MODULE_API_PREFIX}/list`, {
    canteenId,
    date,
  });
}

/**
 * 提交预约请求
 * @param bookingRequest 预约请求数据
 * @returns 预约结果
 */
export function submitBooking(bookingRequest: IMealBookingRequest) {
  if (isMock) {
    return new Promise<IMealBookingResponse>((resolve) => {
      // 模拟部分成功、部分失败的情况
      const response: IMealBookingResponse = {
        success: true,
        message: "预约提交成功",
        successMeals: bookingRequest.mealPeriodIds.slice(0, 1).map((id) => ({
          mealPeriodId: id,
          mealName: id === "1" ? "午餐" : "晚餐",
          bookingTime: `${id === "1" ? "11:00-13:00" : "17:00-19:00"}`,
          bookingDate: bookingRequest.bookingDate,
        })),
        failedMeals: bookingRequest.mealPeriodIds.slice(1).map((id) => ({
          mealPeriodId: id,
          mealName: id === "1" ? "午餐" : "晚餐",
          reason: "席位已满",
          bookingDate: bookingRequest.bookingDate,
        })),
        totalCount: bookingRequest.mealPeriodIds.length,
        successCount: 1,
        failedCount: bookingRequest.mealPeriodIds.length - 1,
      };
      resolve(response);
    });
  }
  return service.post<any, IMealBookingResponse>(
    `${MODULE_API_PREFIX}/booking`,
    bookingRequest
  );
}

/**
 * 提交催发请求
 * @param urgeRequest 催发请求数据
 * @returns 催发结果
 */
export function submitUrgeRequest(urgeRequest: IUrgeRequest) {
  if (isMock) {
    return new Promise<{ success: boolean; message: string }>((resolve) => {
      resolve({
        success: true,
        message: "催发请求提交成功，我们会尽快为您安排席位",
      });
    });
  }
  return service.post<any, { success: boolean; message: string }>(
    `${MODULE_API_PREFIX}/urge`,
    urgeRequest
  );
}

/**
 * 取消预约
 * @param bookingId 预约ID
 * @returns 取消结果
 */
export function cancelBooking(bookingId: string) {
  if (isMock) {
    return new Promise<{ success: boolean; message: string }>((resolve) => {
      resolve({
        success: true,
        message: "预约已成功取消",
      });
    });
  }
  return service.post<any, { success: boolean; message: string }>(
    `${MODULE_API_PREFIX}/cancel`,
    { bookingId }
  );
}

/**
 * 获取用户预约记录
 * @param params 查询参数
 * @returns 预约记录列表
 */
export function getUserBookings(params: {
  pageNo?: number;
  pageSize?: number;
  status?: string;
  startDate?: string;
  endDate?: string;
}) {
  if (isMock) {
    return new Promise<any>((resolve) => {
      resolve({
        total: 10,
        list: [
          {
            id: "1",
            canteenName: "学苑食堂",
            mealName: "午餐",
            bookingDate: "2024-08-28",
            bookingTime: "11:00-13:00",
            status: "CONFIRMED",
            createTime: "2024-08-27 10:30:00",
          },
          {
            id: "2",
            canteenName: "学苑食堂",
            mealName: "晚餐",
            bookingDate: "2024-08-28",
            bookingTime: "17:00-19:00",
            status: "PENDING",
            createTime: "2024-08-27 10:35:00",
          },
        ],
      });
    });
  }
  return service.get<any, any>(`${MODULE_API_PREFIX}/user-bookings`, params);
}
